using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Curio.Application.Interfaces;
using Curio.Application.Implementation;
using Curio.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var connectionString = "Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=**********";

// Configure Orleans Client
builder.UseOrleansClient(clientBuilder =>
{
    clientBuilder
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = connectionString;
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = "curio-cluster";
            options.ServiceId = "curio-service";
        });
});

// Register application services
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
