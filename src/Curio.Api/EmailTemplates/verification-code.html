<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 20px;
        }
        .title {
            font-size: 20px;
            margin-bottom: 20px;
            color: #1f2937;
        }
        .verification-code {
            text-align: center;
            margin: 30px 0;
        }
        .code {
            display: inline-block;
            background-color: #f3f4f6;
            border: 2px dashed #d1d5db;
            padding: 15px 25px;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 5px;
            color: #2563eb;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
        }
        .message {
            margin: 20px 0;
            line-height: 1.6;
        }
        .expiry-notice {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 12px 16px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .security-note {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 12px 16px;
            margin: 20px 0;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Curio</div>
            <h1 class="title">
                {{#ifEquals purpose "registration"}}
                    欢迎注册 Curio！
                {{else}}
                    验证码登录
                {{/ifEquals}}
            </h1>
        </div>

        <div class="message">
            <p>您好，</p>
            <p>
                {{#ifEquals purpose "registration"}}
                    感谢您选择 Curio！为了确保账户安全，请使用以下验证码完成注册：
                {{else}}
                    您正在尝试登录 Curio，请使用以下验证码：
                {{/ifEquals}}
            </p>
        </div>

        <div class="verification-code">
            <div class="code">{{code}}</div>
        </div>

        <div class="expiry-notice">
            <strong>注意：</strong>此验证码将在 <strong>{{formatDate expiresAt "yyyy-MM-dd HH:mm:ss"}}</strong> 过期，请及时使用。
        </div>

        <div class="message">
            <p>请在应用中输入此验证码以继续。如果您没有请求此验证码，请忽略此邮件。</p>
        </div>

        <div class="security-note">
            <strong>安全提醒：</strong>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li>请勿将验证码分享给任何人</li>
                <li>Curio 工作人员不会主动索要您的验证码</li>
                <li>如有疑问，请联系我们的客服团队</li>
            </ul>
        </div>

        <div class="footer">
            <p>此邮件由系统自动发送，请勿直接回复。</p>
            <p>&copy; {{formatDate "now" "yyyy"}} Curio. 保留所有权利。</p>
        </div>
    </div>
</body>
</html>