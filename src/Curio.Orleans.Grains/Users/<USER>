using Orleans;
using Orleans.EventSourcing;
using Orleans.Streams;
using Curio.Orleans.Interfaces.Users;
using Curio.Domain.Users;
using Curio.Shared.Users;

namespace Curio.Orleans.Grains.Users;

public class UserGrain : JournaledGrain<UserState, DomainEvent>, IUserGrain
{
    private IAsyncStream<DomainEvent>? _stream;

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        
        var streamProvider = this.GetStreamProvider("KafkaStreams");
        _stream = streamProvider.GetStream<DomainEvent>("user-events", this.GetPrimaryKeyString());
    }

    public async Task<EmailExistsResult> CheckEmailExistsAsync(CheckEmailExistsCommand command)
    {
        // Check if this command has already been processed
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return new EmailExistsResult
            {
                Exists = !string.IsNullOrEmpty(State.Email),
                IsVerified = State.IsVerified
            };
        }

        return new EmailExistsResult
        {
            Exists = !string.IsNullOrEmpty(State.Email),
            IsVerified = State.IsVerified
        };
    }

    public async Task<VerificationResult> RegisterUserAsync(RegisterUserCommand command)
    {
        // Check if this command has already been processed
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return new VerificationResult
            {
                Success = false,
                Message = "Command already processed"
            };
        }

        // Business rule validation
        if (!State.CanRegister(command.Email, command.Name))
        {
            return new VerificationResult
            {
                Success = false,
                Message = "Cannot register user with provided information"
            };
        }

        // Get verification grain to verify the code
        var verificationGrain = GrainFactory.GetGrain<IVerificationGrain>(command.Email);
        var isValidCode = await verificationGrain.VerifyCodeAsync(command.VerificationCode, "registration");

        if (!isValidCode)
        {
            return new VerificationResult
            {
                Success = false,
                Message = "Invalid or expired verification code"
            };
        }

        // Create and apply the registration event
        var registrationEvent = new UserRegisteredEvent
        {
            UserId = this.GetPrimaryKeyString(),
            Email = command.Email,
            Name = command.Name,
            RegisteredAt = DateTime.UtcNow,
            CommandId = command.CommandId
        };

        // Apply the event and persist
        RaiseEvent(registrationEvent);
        await ConfirmEvents();

        // Publish event to stream
        if (_stream != null)
        {
            await _stream.OnNextAsync(registrationEvent);
        }

        return new VerificationResult
        {
            Success = true,
            Message = "User registered successfully",
            User = State.ToDto()
        };
    }

    public async Task<VerificationResult> LoginUserAsync(LoginUserCommand command)
    {
        // Check if this command has already been processed
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return new VerificationResult
            {
                Success = false,
                Message = "Command already processed"
            };
        }

        // Check if user exists and is verified
        if (string.IsNullOrEmpty(State.Email) || !State.IsVerified)
        {
            var loginFailedEvent = new UserLoginAttemptedEvent
            {
                Email = command.Email,
                Success = false,
                FailureReason = "User not found or not verified",
                AttemptedAt = DateTime.UtcNow,
                CommandId = command.CommandId
            };

            RaiseEvent(loginFailedEvent);
            await ConfirmEvents();

            if (_stream != null)
            {
                await _stream.OnNextAsync(loginFailedEvent);
            }

            return new VerificationResult
            {
                Success = false,
                Message = "User not found or not verified"
            };
        }

        // Verify the login code
        var verificationGrain = GrainFactory.GetGrain<IVerificationGrain>(command.Email);
        var isValidCode = await verificationGrain.VerifyCodeAsync(command.VerificationCode, "login");

        var loginEvent = new UserLoginAttemptedEvent
        {
            Email = command.Email,
            Success = isValidCode,
            FailureReason = isValidCode ? null : "Invalid or expired verification code",
            AttemptedAt = DateTime.UtcNow,
            CommandId = command.CommandId
        };

        RaiseEvent(loginEvent);
        await ConfirmEvents();

        if (_stream != null)
        {
            await _stream.OnNextAsync(loginEvent);
        }

        return new VerificationResult
        {
            Success = isValidCode,
            Message = isValidCode ? "Login successful" : "Invalid or expired verification code",
            User = isValidCode ? State.ToDto() : null
        };
    }

    public async Task<UserDto?> GetUserAsync()
    {
        await Task.CompletedTask;
        
        if (string.IsNullOrEmpty(State.Email))
        {
            return null;
        }

        return State.ToDto();
    }
}