using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Curio.Application.Interfaces;
using Curio.Infrastructure.Services;
using Curio.Infrastructure.Configuration;

namespace Curio.Infrastructure;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure email settings
        services.Configure<EmailSettings>(configuration.GetSection(EmailSettings.SectionName));
        
        // Register email services
        services.AddSingleton<IEmailTemplateService, HandlebarsEmailTemplateService>();
        services.AddScoped<ISmtpEmailService, SmtpEmailService>();
        
        // For backward compatibility, register the old IEmailService interface
        services.AddScoped<IEmailService>(provider => 
        {
            var smtpService = provider.GetRequiredService<ISmtpEmailService>();
            return new EmailServiceAdapter(smtpService);
        });
        
        // Add memory cache for template caching
        services.AddMemoryCache();
        
        return services;
    }
}