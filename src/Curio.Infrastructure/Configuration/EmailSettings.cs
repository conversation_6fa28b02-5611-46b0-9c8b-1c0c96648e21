namespace Curio.Infrastructure.Configuration;

public class EmailSettings
{
    public const string SectionName = "Email";
    
    public SmtpSettings Smtp { get; set; } = new();
    public TemplateSettings Templates { get; set; } = new();
    public SenderSettings DefaultSender { get; set; } = new();
    public RetrySettings Retry { get; set; } = new();
}

public class SmtpSettings
{
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; } = 587;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool EnableSsl { get; set; } = true;
    public int TimeoutSeconds { get; set; } = 30;
    public bool UseDefaultCredentials { get; set; } = false;
}

public class TemplateSettings
{
    public string TemplatesDirectory { get; set; } = "EmailTemplates";
    public bool EnableCaching { get; set; } = true;
    public int CacheExpirationMinutes { get; set; } = 60;
}

public class SenderSettings
{
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public string ReplyToEmail { get; set; } = string.Empty;
    public string ReplyToName { get; set; } = string.Empty;
}

public class RetrySettings
{
    public int MaxAttempts { get; set; } = 3;
    public int DelayMilliseconds { get; set; } = 1000;
    public bool ExponentialBackoff { get; set; } = true;
}