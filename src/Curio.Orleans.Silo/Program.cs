﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Orleans.Streams.Kafka.Config;
using Curio.Infrastructure;

var builder = Host.CreateApplicationBuilder(args);

var connectionString = "Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=**********";
var kafkaBrokers = "localhost:9092";

// 配置Orleans Silo
builder.UseOrleans(siloBuilder =>
{
    siloBuilder
        // 集群配置 - 使用PostgreSQL clustering
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = connectionString;
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = "curio-cluster";
            options.ServiceId = "curio-service";
        })
        
        // Orleans状态存储配置
        .AddAdoNetGrainStorage("Default", options =>
        {
            options.ConnectionString = connectionString;
        })
        .AddAdoNetGrainStorage("JournaledGrainState", options =>
        {
            options.ConnectionString = connectionString;
        })
        
        // JournaledGrain Event Sourcing配置
        .AddLogStorageBasedLogConsistencyProvider("EventSourcing")
        
        // Kafka Streams配置
        .AddKafka("KafkaStreams")
        .WithOptions(options =>
        {
            options.BrokerList = new[] { kafkaBrokers };
            options.ConsumerGroupId = "orleans-event-streams";
            
            // Topics配置
            options.AddTopic("domain-events");
            options.AddTopic("verification-events");  
            options.AddTopic("user-events");
        })
        .AddJson()
        .Build()
        
        // PubSub存储
        .AddMemoryGrainStorage("PubSubStore")
        
        // Orleans Reminders（定时任务）
        .UseAdoNetReminderService(options =>
        {
            options.ConnectionString = connectionString;
        })
        
        // 配置日志
        .ConfigureLogging(logging => logging.AddConsole());
});

// 注册基础设施服务
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// 启动Orleans Silo
Console.WriteLine("🚀 Starting Curio Orleans Silo...");
Console.WriteLine($"📊 Cluster: curio-cluster");
Console.WriteLine($"🏷️  Service: curio-service");
Console.WriteLine($"🗄️  Database: PostgreSQL (localhost:5432)");
Console.WriteLine($"🔗 Clustering: PostgreSQL ADO.NET");
Console.WriteLine($"📨 Kafka: {kafkaBrokers}");
Console.WriteLine($"⚡ Event Sourcing: Enabled with Kafka streaming");
Console.WriteLine();

await app.RunAsync();
